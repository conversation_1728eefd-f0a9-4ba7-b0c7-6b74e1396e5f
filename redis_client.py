import grpc
import asyncio
import pandas as pd
from datetime import datetime, timedelta
import service_pb2
import service_pb2_grpc
from redis import asyncio as aioredis
import time

redis_ip = "*************"
redis_port = 6379
db_name = ""


def get_tick_data(timestamp):
    with grpc.insecure_channel(grpc_server) as channel:
        stub = service_pb2_grpc.DataSnapStub(channel)
        req = service_pb2.DataSnapRequest()
        req.timestamp = str(timestamp)
        req.segment = "MCX"
        response = stub.get_tick_data(req)
    return response


def get_latest_second():
    with grpc.insecure_channel(grpc_server) as channel:
        stub = service_pb2_grpc.DataSnapStub(channel)
        req = service_pb2.TimeStampRequest()
        response = stub.get_latest_second(req)
    return response


async def create_redis_pool():
    return await aioredis.Redis(host=redis_ip, port=redis_port, db=db_name)


async def main():
    redis_pool = await create_redis_pool()
    # current_time = pd.Timestamp((get_latest_second()).timestamp)
    # print(current_time)
    while True:
        try:
            while True:
                # print(current_time)
                latest_timestamp = (get_latest_second()).timestamp
                result = get_tick_data(latest_timestamp)
                async with redis_pool as conn:
                    coroutines = []
                    for tick in result.message:
                        tick = str(tick)
                        contract = tick.split("|")[1]
                        coroutines.append(conn.set(str(contract), tick))
                    await asyncio.gather(*coroutines)
                # current_time += timedelta(seconds=1)
        except Exception as e:
            print(e)


if __name__ == "__main__":
    grpc_server = "*************:5000"
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main())