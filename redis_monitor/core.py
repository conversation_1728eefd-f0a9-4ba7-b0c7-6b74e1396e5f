"""
Redis Monitoring Core Module

This module provides the core functionality for monitoring Redis price data freshness
and detecting stale data for trading systems.
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass
from redis import asyncio as aioredis
import re


@dataclass
class StaleKeyInfo:
    """Information about a stale Redis key"""
    key: str
    last_update: datetime
    age_minutes: float
    value_preview: str


@dataclass
class MonitoringStats:
    """Statistics from monitoring run"""
    total_keys_scanned: int
    stale_keys_count: int
    scan_duration_seconds: float
    redis_info: Dict[str, Any]
    timestamp: datetime


class RedisMonitor:
    """
    Core Redis monitoring class for detecting stale price data.
    
    This class handles:
    - Redis connection management with connection pooling
    - Efficient key discovery using SCAN operations
    - Data freshness analysis by parsing timestamps
    - Redis health monitoring
    """
    
    def __init__(
        self,
        redis_host: str = "*************",
        redis_port: int = 6379,
        redis_db: int = 0,
        stale_threshold_minutes: int = 3,
        key_patterns: Optional[List[str]] = None,
        max_connections: int = 10,
        scan_batch_size: int = 1000
    ):
        """
        Initialize Redis Monitor
        
        Args:
            redis_host: Redis server hostname/IP
            redis_port: Redis server port
            redis_db: Redis database number
            stale_threshold_minutes: Minutes after which data is considered stale
            key_patterns: List of patterns to match keys (e.g., ['*PE*', '*CE*'])
            max_connections: Maximum Redis connections in pool
            scan_batch_size: Number of keys to scan in each batch
        """
        self.redis_host = redis_host
        self.redis_port = redis_port
        self.redis_db = redis_db
        self.stale_threshold_minutes = stale_threshold_minutes
        self.key_patterns = key_patterns or ['*PE*', '*CE*', 'CRUDEOIL*']
        self.max_connections = max_connections
        self.scan_batch_size = scan_batch_size
        
        self.redis_pool: Optional[aioredis.ConnectionPool] = None
        self.logger = logging.getLogger(__name__)
        
        # Cache for discovered keys to reduce scan frequency
        self._key_cache: Set[str] = set()
        self._last_key_discovery: Optional[datetime] = None
        self._key_cache_ttl_minutes = 5
        
    async def initialize(self) -> None:
        """Initialize Redis connection pool"""
        try:
            self.redis_pool = aioredis.ConnectionPool(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_db,
                max_connections=self.max_connections,
                retry_on_timeout=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            
            # Test connection
            redis_client = aioredis.Redis(connection_pool=self.redis_pool)
            await redis_client.ping()
            await redis_client.close()
            
            self.logger.info(f"Redis connection pool initialized: {self.redis_host}:{self.redis_port}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Redis connection: {e}")
            raise
    
    async def close(self) -> None:
        """Close Redis connection pool"""
        if self.redis_pool:
            await self.redis_pool.disconnect()
            self.logger.info("Redis connection pool closed")
    
    async def discover_keys(self, force_refresh: bool = False) -> Set[str]:
        """
        Discover Redis keys matching configured patterns
        
        Args:
            force_refresh: Force refresh of key cache
            
        Returns:
            Set of discovered keys
        """
        # Check if we can use cached keys
        if (not force_refresh and 
            self._last_key_discovery and 
            self._key_cache and
            datetime.now() - self._last_key_discovery < timedelta(minutes=self._key_cache_ttl_minutes)):
            self.logger.debug(f"Using cached keys: {len(self._key_cache)} keys")
            return self._key_cache
        
        redis_client = aioredis.Redis(connection_pool=self.redis_pool)
        discovered_keys: Set[str] = set()
        
        try:
            for pattern in self.key_patterns:
                self.logger.debug(f"Scanning for pattern: {pattern}")
                
                cursor = 0
                while True:
                    cursor, keys = await redis_client.scan(
                        cursor=cursor,
                        match=pattern,
                        count=self.scan_batch_size
                    )
                    
                    # Decode bytes to strings
                    decoded_keys = [key.decode('utf-8') if isinstance(key, bytes) else key for key in keys]
                    discovered_keys.update(decoded_keys)
                    
                    if cursor == 0:
                        break
                
                self.logger.debug(f"Pattern {pattern} found {len([k for k in discovered_keys if self._matches_pattern(k, pattern)])} keys")
            
            # Update cache
            self._key_cache = discovered_keys
            self._last_key_discovery = datetime.now()
            
            self.logger.info(f"Discovered {len(discovered_keys)} total keys")
            return discovered_keys
            
        except Exception as e:
            self.logger.error(f"Error discovering keys: {e}")
            raise
        finally:
            await redis_client.close()
    
    def _matches_pattern(self, key: str, pattern: str) -> bool:
        """Check if key matches pattern (simple wildcard matching)"""
        regex_pattern = pattern.replace('*', '.*')
        return bool(re.match(regex_pattern, key))
    
    async def check_key_freshness(self, keys: Set[str]) -> Tuple[List[StaleKeyInfo], MonitoringStats]:
        """
        Check freshness of provided keys
        
        Args:
            keys: Set of Redis keys to check
            
        Returns:
            Tuple of (stale_keys_info, monitoring_stats)
        """
        start_time = time.time()
        redis_client = aioredis.Redis(connection_pool=self.redis_pool)
        stale_keys: List[StaleKeyInfo] = []
        
        try:
            # Get Redis info for health monitoring
            redis_info = await redis_client.info()
            
            # Check each key in batches for better performance
            key_list = list(keys)
            batch_size = 100  # Process keys in smaller batches
            
            for i in range(0, len(key_list), batch_size):
                batch_keys = key_list[i:i + batch_size]
                
                # Use pipeline for efficient batch operations
                pipe = redis_client.pipeline()
                for key in batch_keys:
                    pipe.get(key)
                
                values = await pipe.execute()
                
                # Process batch results
                for key, value in zip(batch_keys, values):
                    if value is None:
                        continue
                    
                    try:
                        # Decode value if it's bytes
                        if isinstance(value, bytes):
                            value = value.decode('utf-8')
                        
                        # Parse timestamp from first field (format: "2025-07-17 17:15:10|...")
                        timestamp_str = value.split('|')[0]
                        last_update = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        
                        # Calculate age
                        age = datetime.now() - last_update
                        age_minutes = age.total_seconds() / 60
                        
                        # Check if stale
                        if age_minutes > self.stale_threshold_minutes:
                            stale_keys.append(StaleKeyInfo(
                                key=key,
                                last_update=last_update,
                                age_minutes=age_minutes,
                                value_preview=value[:100] + "..." if len(value) > 100 else value
                            ))
                    
                    except (ValueError, IndexError) as e:
                        self.logger.warning(f"Failed to parse timestamp for key {key}: {e}")
                        continue
            
            scan_duration = time.time() - start_time
            
            # Create monitoring stats
            stats = MonitoringStats(
                total_keys_scanned=len(keys),
                stale_keys_count=len(stale_keys),
                scan_duration_seconds=scan_duration,
                redis_info=redis_info,
                timestamp=datetime.now()
            )
            
            self.logger.info(
                f"Freshness check completed: {len(keys)} keys scanned, "
                f"{len(stale_keys)} stale keys found in {scan_duration:.2f}s"
            )
            
            return stale_keys, stats
            
        except Exception as e:
            self.logger.error(f"Error checking key freshness: {e}")
            raise
        finally:
            await redis_client.close()
    
    async def get_redis_health_info(self) -> Dict[str, Any]:
        """Get Redis health and performance information"""
        redis_client = aioredis.Redis(connection_pool=self.redis_pool)
        
        try:
            info = await redis_client.info()
            
            # Extract key health metrics
            health_info = {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory_human': info.get('used_memory_human', 'Unknown'),
                'used_memory_peak_human': info.get('used_memory_peak_human', 'Unknown'),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'instantaneous_ops_per_sec': info.get('instantaneous_ops_per_sec', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'last_save_time': info.get('rdb_last_save_time', 0),
                'uptime_in_seconds': info.get('uptime_in_seconds', 0),
            }
            
            # Calculate hit ratio
            hits = health_info['keyspace_hits']
            misses = health_info['keyspace_misses']
            if hits + misses > 0:
                health_info['hit_ratio'] = hits / (hits + misses)
            else:
                health_info['hit_ratio'] = 0.0
            
            # Convert last save time to readable format
            if health_info['last_save_time'] > 0:
                health_info['last_save_time_formatted'] = datetime.fromtimestamp(
                    health_info['last_save_time']
                ).strftime('%Y-%m-%d %H:%M:%S')
            else:
                health_info['last_save_time_formatted'] = 'Never'
            
            return health_info
            
        except Exception as e:
            self.logger.error(f"Error getting Redis health info: {e}")
            raise
        finally:
            await redis_client.close()
    
    async def run_monitoring_cycle(self, force_key_refresh: bool = False) -> Tuple[List[StaleKeyInfo], MonitoringStats]:
        """
        Run a complete monitoring cycle
        
        Args:
            force_key_refresh: Force refresh of key discovery cache
            
        Returns:
            Tuple of (stale_keys, monitoring_stats)
        """
        self.logger.info("Starting monitoring cycle")
        
        try:
            # Discover keys
            keys = await self.discover_keys(force_refresh=force_key_refresh)
            
            if not keys:
                self.logger.warning("No keys discovered - Redis may be empty or patterns don't match")
                return [], MonitoringStats(
                    total_keys_scanned=0,
                    stale_keys_count=0,
                    scan_duration_seconds=0,
                    redis_info={},
                    timestamp=datetime.now()
                )
            
            # Check freshness
            stale_keys, stats = await self.check_key_freshness(keys)
            
            self.logger.info(f"Monitoring cycle completed: {stats.stale_keys_count}/{stats.total_keys_scanned} stale keys")
            
            return stale_keys, stats
            
        except Exception as e:
            self.logger.error(f"Error in monitoring cycle: {e}")
            raise
