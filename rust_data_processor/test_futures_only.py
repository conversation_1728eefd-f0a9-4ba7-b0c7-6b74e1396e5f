#!/usr/bin/env python3
"""
Test script for futures-only data handling in process_and_resample_chunk_fast function.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import rust_data_processor

def create_futures_only_data():
    """Create a sample futures-only DataFrame (no strike_price or option_type columns)."""
    
    # Create sample timestamps
    start_time = datetime(2024, 1, 1, 9, 0, 0)
    timestamps = [start_time + timedelta(minutes=i) for i in range(100)]
    
    # Create sample futures data
    data = {
        'timestamp': timestamps,
        'symbol': ['NIFTY'] * 100,
        'expiry': ['2024-01-25'] * 100,
        'close': np.random.uniform(18000, 18500, 100),
        'volume': np.random.randint(100, 1000, 100),
        'ord_price': np.random.uniform(18000, 18500, 100),
    }
    
    # Add some zero values to test filtering
    data['close'][::10] = 0.0  # Every 10th close price is 0
    data['ord_price'][1::10] = 0.0  # Every 10th ord_price starting from index 1 is 0
    
    return pd.DataFrame(data)

def create_mixed_data():
    """Create a sample DataFrame with both futures and options data."""
    
    # Create sample timestamps
    start_time = datetime(2024, 1, 1, 9, 0, 0)
    timestamps = [start_time + timedelta(minutes=i) for i in range(100)]
    
    # Create mixed data (50 futures, 50 options)
    data = {
        'timestamp': timestamps,
        'symbol': ['NIFTY'] * 100,
        'expiry': ['2024-01-25'] * 100,
        'close': np.random.uniform(18000, 18500, 100),
        'volume': np.random.randint(100, 1000, 100),
        'ord_price': np.random.uniform(18000, 18500, 100),
        'strike_price': [None] * 50 + [18000.0] * 50,  # First 50 are futures (None), last 50 are options
        'option_type': [None] * 50 + ['CE'] * 50,  # First 50 are futures (None), last 50 are options
    }
    
    # Add some zero values to test filtering
    data['close'][::10] = 0.0
    data['ord_price'][1::10] = 0.0
    
    return pd.DataFrame(data)

def test_futures_only():
    """Test the function with futures-only data."""
    print("Testing futures-only data...")
    
    # Create futures-only DataFrame
    df_futures = create_futures_only_data()
    print(f"Created futures-only DataFrame with shape: {df_futures.shape}")
    print(f"Columns: {list(df_futures.columns)}")
    
    # Convert to polars DataFrame for the Rust function
    import polars as pl
    df_polars = pl.from_pandas(df_futures)
    
    # Test the function
    try:
        result = rust_data_processor.process_and_resample_chunk_fast(df_polars, 5)  # 5-minute intervals
        
        print("\nResults:")
        for key, value in result.items():
            df_result = value.to_pandas()
            print(f"{key}: {df_result.shape[0]} rows")
            if df_result.shape[0] > 0:
                print(f"  Columns: {list(df_result.columns)}")
                print(f"  Sample data:\n{df_result.head(2)}")
            else:
                print("  Empty DataFrame")
        
        print("\n✅ Futures-only test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Futures-only test failed: {e}")
        return False

def test_mixed_data():
    """Test the function with mixed futures and options data."""
    print("\nTesting mixed futures and options data...")
    
    # Create mixed DataFrame
    df_mixed = create_mixed_data()
    print(f"Created mixed DataFrame with shape: {df_mixed.shape}")
    print(f"Columns: {list(df_mixed.columns)}")
    
    # Convert to polars DataFrame for the Rust function
    import polars as pl
    df_polars = pl.from_pandas(df_mixed)
    
    # Test the function
    try:
        result = rust_data_processor.process_and_resample_chunk_fast(df_polars, 5)  # 5-minute intervals
        
        print("\nResults:")
        for key, value in result.items():
            df_result = value.to_pandas()
            print(f"{key}: {df_result.shape[0]} rows")
            if df_result.shape[0] > 0:
                print(f"  Columns: {list(df_result.columns)}")
                print(f"  Sample data:\n{df_result.head(2)}")
            else:
                print("  Empty DataFrame")
        
        print("\n✅ Mixed data test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Mixed data test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing process_and_resample_chunk_fast with different data types...\n")
    
    # Test both scenarios
    futures_test_passed = test_futures_only()
    mixed_test_passed = test_mixed_data()
    
    print(f"\n{'='*50}")
    print("Test Summary:")
    print(f"Futures-only test: {'✅ PASSED' if futures_test_passed else '❌ FAILED'}")
    print(f"Mixed data test: {'✅ PASSED' if mixed_test_passed else '❌ FAILED'}")
    
    if futures_test_passed and mixed_test_passed:
        print("\n🎉 All tests passed! The function correctly handles both futures-only and mixed data.")
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
