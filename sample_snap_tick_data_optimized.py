import logging
import os
import glob
import re
import pandas as pd
from arcticdb import Arctic
from multiprocessing import Pool
import datetime
import sys



# Add the rust processor to the path
sys.path.append('/home/<USER>/repos/data_auditing/rust_data_processor')

# Import the optimized data processor - Rust only
from python_wrapper import process_and_resample_chunk

print("Using Rust-optimized data processing")


library = "nse/1_min/snap_file/trd_ord"
month_range = "def_def"
sampled_location = f"snap_{month_range}_date_wise_sampled"
if not os.path.exists(f"/home/<USER>/repos/data_auditing/{sampled_location}"):
    os.makedirs(f"/home/<USER>/repos/data_auditing/{sampled_location}")
    
storet = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")


def process_snap_file_optimized(sym):
    """
    Optimized version of process_snap_file using Rust backend for performance-critical operations.
    """
    try:

        print(f"Started symbol: {sym}")
        for date_wise_file in os.listdir(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined"):
            df_chunk = pd.read_parquet(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined/{date_wise_file}")

            df_chunk.index = df_chunk.index + pd.Timedelta(-1, 'ns')

            resampled_data = process_and_resample_chunk(df_chunk, interval_minutes=1)

            # Save resampled data directly - no additional processing needed
            if not resampled_data['fut_trd'].empty:
                if not os.path.exists(f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/fut_trd"):
                    os.makedirs(f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/fut_trd")
                resampled_data['fut_trd'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/fut_trd/{date_wise_file}", engine='fastparquet'
                )

            if not resampled_data['fut_ord'].empty:
                if not os.path.exists(f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/fut_ord"):
                    os.makedirs(f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/fut_ord")
                resampled_data['fut_ord'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/fut_ord/{date_wise_file}", engine='fastparquet'
                )

            if not resampled_data['opt_trd'].empty:
                if not os.path.exists(f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/opt_trd"):
                    os.makedirs(f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/opt_trd")
                resampled_data['opt_trd'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/opt_trd/{date_wise_file}", engine='fastparquet'
                )

            if not resampled_data['opt_ord'].empty:
                if not os.path.exists(f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/opt_ord"):
                    os.makedirs(f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/opt_ord")
                resampled_data['opt_ord'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}/opt_ord/{date_wise_file}", engine='fastparquet'
                )
            
            os.remove(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined/{date_wise_file}")

        print(f"Completed symbol: {sym}\n")
    except Exception as e:
        print(f"Failed for {sym} due to: {e}\n")
        return


if __name__ == "__main__":
    # Example usage:
    # process_snap_file_optimized("MIDCPNIFTY")

    syms = os.listdir(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled")
    # process_snap_file_optimized(syms[0])

    # For parallel processing:
    with Pool(18) as P:
        P.map(process_snap_file_optimized, syms)
    
    # For sequential processing
    # for sym in syms:
    #     process_snap_file_optimized(sym)

