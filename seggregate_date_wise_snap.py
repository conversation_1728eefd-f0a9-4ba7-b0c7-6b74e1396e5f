import multiprocessing
import pandas as pd
import logging
from arcticdb import Arctic
import os
import gc

store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
library = "nse/1_min/snap_file/trd_ord"
month_range = "def_def"
lib = store[library]
syms = lib.list_symbols()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()


def get_from_snap_file(sym):
    # if sym in ["NIFTY", "BANKNIFTY"]: return
    if os.path.exists(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/"):
        return

    tot_len = lib.get_description(sym).row_count
    logger.info(f"Started for {sym} having length {tot_len}")

    count = 4 if sym not in ["NIFTY", "BANKNIFTY"] else 35

    for ct in range(1, count + 1):
        logger.info(f"Started for {sym} for chunk {ct}")
        # Using the context manager to handle memory and file reading
        df = lib.read(sym, row_range=[(ct - 1) * tot_len // count, ct * tot_len // count]).data
        for date, dfg in df.groupby(df.timestamp.dt.date):
            if not os.path.exists(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ct}"):
                os.makedirs(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ct}", exist_ok=True)
            dfg.to_parquet(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ct}/{date}.parquet")

        df = pd.DataFrame()
        logger.info(f"Completed for {sym} for chunk {ct}")

    logger.info(f"Completed for {sym}")


def combine_date_wise_snap(sym):
    logger.info(f"Started combining for {sym}")

    count = 4 if sym not in ["NIFTY", "BANKNIFTY"] else 35

    for ct in range(1, count+1):
        logger.info(f"Started combining for {sym} for chunk {ct}")
        for file in os.listdir(
            f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ct}"
        ):
            df_list=[]
            for ctj in list(range(ct, count+1)):
                if os.path.exists(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ctj}/{file}"):
                    df_list.append(pd.read_parquet(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ctj}/{file}"))

            df = pd.concat(df_list).drop_duplicates()
            df = df.set_index("timestamp").sort_index()

            if not os.path.exists(
                f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined"
            ):
                os.makedirs(
                    f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined"
                )
            df.to_parquet(
                f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/combined/{file[:10]}.parquet"
            )
            del df
            del df_list
            gc.collect()
            
            for ctj in range(ct, count+1):
                if os.path.exists(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ctj}/{file}"):
                    os.remove(f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled/{sym}/{ctj}/{file}")
                
        logger.info(f"Completed combining for {sym} for chunk {ct}")

    logger.info(f"Completed combining for {sym}")



# get_from_snap_file("MIDCPNIFTY")

[get_from_snap_file(sym) for sym in syms]


# syms = os.listdir(
#     f"/home/<USER>/repos/data_auditing/snap_{month_range}_date_wise_unsampled"
# )

# with multiprocessing.Pool(15) as p:
#     p.map(combine_date_wise_snap, syms)
